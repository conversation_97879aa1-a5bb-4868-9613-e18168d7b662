// Export all custom hooks from a central location
export { useA<PERSON>, AuthProvider } from "./useAuth.js";
export {
  useFormHandler,
  useAuthForm,
  useSearchForm,
} from "./useFormHandler.js";
export {
  useDataFetching,
  usePaginatedData,
  useCrudOperations,
} from "./useDataFetching.js";
export { useFileOperations } from "./useFileOperations.js";

// Export Redux store
export { default as store } from "../redux/stores/store.js";
