# Redux Toolkit with RTK Query Setup

This document provides an overview of the Redux Toolkit with RTK Query implementation for the CPA Dashboard application.

## 📁 Folder Structure

```
frontend/src/redux/
├── api-services/           # RTK Query API definitions
│   ├── api.js             # Base API configuration
│   ├── authApi.js         # Authentication endpoints
│   ├── mastersApi.js      # Master data endpoints
│   ├── quickbooksApi.js   # QuickBooks endpoints
│   └── index.js           # Centralized exports
├── reducers/
│   └── rootReducer.js     # Combined reducers
├── slices/                # Redux Toolkit slices
│   ├── authSlice.js       # Authentication state
│   ├── mastersSlice.js    # Master data state
│   ├── uiSlice.js         # UI state management
│   └── quickbooksAccountSlice.js # QuickBooks state
├── stores/
│   └── store.js           # Store configuration
└── thunks/                # Custom thunks (minimal usage)
```

## 🚀 Key Features

### ✅ Completed Setup

1. **Base API Service** (`api-services/api.js`)
   - Configured with `fetchBaseQuery`
   - Automatic token management
   - Token refresh handling
   - Error handling and retry logic

2. **Authentication API** (`api-services/authApi.js`)
   - Login, logout, profile management
   - Password reset and change
   - Token refresh automation

3. **Masters API** (`api-services/mastersApi.js`)
   - CRUD operations for organizations
   - User management
   - Roles and permissions
   - Master data fetching

4. **QuickBooks API** (`api-services/quickbooksApi.js`)
   - Account management
   - OAuth URL generation
   - Sync operations
   - Reports (Trial Balance, P&L, Balance Sheet)

5. **Enhanced Slices**
   - **Auth Slice**: Token persistence, user state
   - **Masters Slice**: Selection state, filters, caching
   - **UI Slice**: Modals, toasts, loading states, theme
   - **QuickBooks Slice**: Account state, sync status, filters

## 🔧 Usage Examples

### Authentication

```javascript
import { useLoginMutation, useGetProfileQuery } from '@/redux/api-services';

function LoginComponent() {
  const [login, { isLoading, error }] = useLoginMutation();
  const { data: profile, isLoading: profileLoading } = useGetProfileQuery();

  const handleLogin = async (credentials) => {
    try {
      const result = await login(credentials).unwrap();
      console.log('Login successful:', result);
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  return (
    // Your login form JSX
  );
}
```

### Master Data Management

```javascript
import { 
  useGetOrganizationsQuery, 
  useCreateOrganizationMutation 
} from '@/redux/api-services';

function OrganizationsComponent() {
  const { 
    data: organizations, 
    isLoading, 
    error 
  } = useGetOrganizationsQuery({
    page: 1,
    pageSize: 10
  });

  const [createOrganization] = useCreateOrganizationMutation();

  const handleCreate = async (orgData) => {
    try {
      await createOrganization(orgData).unwrap();
      // Data will automatically refresh due to cache invalidation
    } catch (error) {
      console.error('Failed to create organization:', error);
    }
  };

  return (
    // Your organizations list JSX
  );
}
```

### QuickBooks Integration

```javascript
import { 
  useGetQuickbooksAccountsQuery,
  useSyncQuickbooksAccountMutation 
} from '@/redux/api-services';

function QuickBooksComponent() {
  const { data: accounts, isLoading } = useGetQuickbooksAccountsQuery({
    organization_id: 'org-123'
  });

  const [syncAccount, { isLoading: syncing }] = useSyncQuickbooksAccountMutation();

  const handleSync = async (accountId) => {
    try {
      await syncAccount({ 
        id: accountId, 
        organization_id: 'org-123' 
      }).unwrap();
    } catch (error) {
      console.error('Sync failed:', error);
    }
  };

  return (
    // Your QuickBooks accounts JSX
  );
}
```

### UI State Management

```javascript
import { useDispatch, useSelector } from 'react-redux';
import { 
  openModal, 
  addToast, 
  setLoading 
} from '@/redux/slices/uiSlice';

function SomeComponent() {
  const dispatch = useDispatch();
  const { modals, toasts } = useSelector(state => state.ui);

  const handleOpenModal = () => {
    dispatch(openModal({
      modalName: 'organizationModal',
      mode: 'create',
      data: null
    }));
  };

  const showSuccessToast = () => {
    dispatch(addToast({
      type: 'success',
      title: 'Success!',
      message: 'Operation completed successfully'
    }));
  };

  return (
    // Your component JSX
  );
}
```

## 🔄 Migration from Async Thunks

### Before (Async Thunks)
```javascript
// Old way
import { fetchQuickbooksAccounts } from '@/lib/api/quickbooks';

const dispatch = useDispatch();
const { quickbooksAccounts, loading, error } = useSelector(state => state.quickbooksAccount);

useEffect(() => {
  dispatch(fetchQuickbooksAccounts({ organization_id: 'org-123' }));
}, [dispatch]);
```

### After (RTK Query)
```javascript
// New way
import { useGetQuickbooksAccountsQuery } from '@/redux/api-services';

const { 
  data: quickbooksAccounts, 
  isLoading: loading, 
  error 
} = useGetQuickbooksAccountsQuery({ organization_id: 'org-123' });

// No useEffect needed - automatic data fetching!
```

## 🎯 Benefits

1. **Automatic Caching**: Data is cached and shared across components
2. **Background Updates**: Automatic refetching and cache invalidation
3. **Loading States**: Built-in loading and error states
4. **Optimistic Updates**: UI updates immediately, rolls back on error
5. **Deduplication**: Multiple identical requests are automatically deduped
6. **Polling**: Easy setup for real-time data updates
7. **Prefetching**: Preload data before it's needed

## 🔧 Configuration

### Environment Variables
Ensure these are set in your `.env` file:
```
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

### Store Setup
The store is already configured in `stores/store.js` with:
- RTK Query middleware
- DevTools (development only)
- Serializable check configuration

## 📝 Next Steps

1. **Migrate Components**: Update existing components to use RTK Query hooks
2. **Remove Old API Files**: Clean up old async thunk files in `@/lib/api/`
3. **Add Error Boundaries**: Implement error boundaries for better error handling
4. **Add Loading Components**: Create reusable loading components
5. **Implement Offline Support**: Add offline capabilities with RTK Query
6. **Add Tests**: Write tests for API endpoints and slices

## 🐛 Troubleshooting

### Common Issues

1. **Token Not Persisting**: Check localStorage implementation in authSlice
2. **Cache Not Updating**: Ensure proper tag invalidation in mutations
3. **Loading States**: Use RTK Query's built-in loading states instead of custom ones
4. **Error Handling**: Use RTK Query's error handling instead of custom error states

### Debug Tools

- Redux DevTools Extension
- RTK Query DevTools (built into Redux DevTools)
- Network tab for API calls
- Console logs for debugging

## 📚 Resources

- [RTK Query Documentation](https://redux-toolkit.js.org/rtk-query/overview)
- [Redux Toolkit Documentation](https://redux-toolkit.js.org/)
- [React Redux Hooks](https://react-redux.js.org/api/hooks)
