// Export all API services and hooks from a central location

// Base API
export { api } from './api';

// Auth API
export { authApi } from './authApi';
export {
  useLoginMutation,
  useGetProfileQuery,
  useUpdateProfileMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useChangePasswordMutation,
  useSignUpMutation,
} from './authApi';

// Masters API
export { mastersApi } from './mastersApi';
export {
  // Organization hooks
  useGetOrganizationsQuery,
  useGetOrganizationByIdQuery,
  useCreateOrganizationMutation,
  useUpdateOrganizationMutation,
  useDeleteOrganizationMutation,
  
  // User hooks
  useGetUsersQuery,
  useGetUserByIdQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  
  // Role and Permission hooks
  useGetRolesQuery,
  useGetPermissionsQuery,
  
  // Master data hook
  useGetMasterDataQuery,
} from './mastersApi';

// QuickBooks API
export { quickbooksApi } from './quickbooksApi';
export {
  // Account management hooks
  useGetQuickbooksAccountsQuery,
  useGetQuickbooksAccountByIdQuery,
  useAddQuickbooksAccountMutation,
  useUpdateQuickbooksAccountStatusMutation,
  useSyncQuickbooksAccountMutation,
  useDeleteQuickbooksAccountMutation,
  
  // OAuth and token hooks
  useGetQuickbooksOAuthUrlQuery,
  useGetQuickbooksTokensMutation,
  useAddQuickbooksTokensMutation,
  
  // File operations
  useSaveQuickbooksFileMutation,
  
  // Reports hooks
  useGetQuickbooksReportsQuery,
  useFetchTrialBalanceMutation,
  useFetchProfitLossMutation,
  useFetchBalanceSheetMutation,
} from './quickbooksApi';

/**
 * API Configuration and Utilities
 */
export const API_CONFIG = {
  // Default pagination settings
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  
  // Request timeouts
  DEFAULT_TIMEOUT: 10000,
  UPLOAD_TIMEOUT: 60000,
  
  // Retry settings
  DEFAULT_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  
  // Cache settings
  DEFAULT_CACHE_TIME: 5 * 60 * 1000, // 5 minutes
  DEFAULT_STALE_TIME: 2 * 60 * 1000, // 2 minutes
};

/**
 * Common API utilities
 */
export const apiUtils = {
  // Helper to invalidate all caches
  invalidateAllCaches: (dispatch) => {
    dispatch(api.util.resetApiState());
  },
  
  // Helper to invalidate specific tags
  invalidateTags: (dispatch, tags) => {
    dispatch(api.util.invalidateTags(tags));
  },
  
  // Helper to prefetch data
  prefetchQuery: (dispatch, endpoint, args) => {
    dispatch(api.util.prefetch(endpoint, args));
  },
};
