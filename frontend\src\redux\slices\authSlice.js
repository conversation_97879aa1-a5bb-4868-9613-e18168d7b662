import { createSlice } from "@reduxjs/toolkit";
import { authApi } from "../api-services/authApi";

const initialState = {
  user: null,
  token: localStorage.getItem("token") || null,
  isAuthenticated: false,
  error: null,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
      state.isAuthenticated = !!action.payload;
    },
    setToken: (state, action) => {
      state.token = action.payload;
      if (action.payload) {
        localStorage.setItem("token", action.payload);
      } else {
        localStorage.removeItem("token");
      }
    },
    clearAuth: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.error = null;
      localStorage.removeItem("token");
    },
    clearError: (state) => {
      state.error = null;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    initializeAuth: (state) => {
      const token = localStorage.getItem("token");
      if (token) {
        state.token = token;
        // Note: user data will be fetched via RTK Query when needed
      }
    },
  },
  extraReducers: (builder) => {
    // Handle login success
    builder.addMatcher(
      authApi.endpoints.login.matchFulfilled,
      (state, action) => {
        const { data } = action.payload;
        if (data?.token) {
          state.token = data.token;
          state.isAuthenticated = true;
        }
        if (data?.user) {
          state.user = data.user;
        }
        state.error = null;
      }
    );

    // Handle login error
    builder.addMatcher(
      authApi.endpoints.login.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || "Login failed";
        state.isAuthenticated = false;
        state.token = null;
        state.user = null;
      }
    );

    // Handle profile fetch success
    builder.addMatcher(
      authApi.endpoints.getProfile.matchFulfilled,
      (state, action) => {
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      }
    );

    // Handle logout
    builder.addMatcher(authApi.endpoints.logout.matchFulfilled, (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.error = null;
    });

    // Handle token refresh
    builder.addMatcher(
      authApi.endpoints.refreshToken.matchFulfilled,
      (state, action) => {
        const { data } = action.payload;
        if (data?.token) {
          state.token = data.token;
        }
        state.error = null;
      }
    );
  },
});

export const {
  setUser,
  setToken,
  clearAuth,
  clearError,
  setError,
  initializeAuth,
} = authSlice.actions;

export default authSlice.reducer;
