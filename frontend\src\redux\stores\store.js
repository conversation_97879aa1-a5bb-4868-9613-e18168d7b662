import { configureStore } from "@reduxjs/toolkit";
import quickbooksAccountReducer from "../slices/quickbooksAccountSlice";
import sageAccountReducer from "../slices/sageAccountSlice";
import netsuiteAccountReducer from "../slices/netsuiteAccountSlice";
import authReducer from "../slices/authSlice";
import organizationsReducer from "../../store/reducers/organizationsReducer";

export const store = configureStore({
  reducer: {
    quickbooksAccount: quickbooksAccountReducer,
    sageAccount: sageAccountReducer,
    netsuiteAccount: netsuiteAccountReducer,
    auth: authReducer,
    organizations: organizationsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST"],
      },
    }),
});

// Export store for use in components
export default store;
